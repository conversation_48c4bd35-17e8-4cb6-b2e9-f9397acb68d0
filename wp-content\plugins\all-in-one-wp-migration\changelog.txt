= 7.75 =
* Custom backups path on database import step

= 7.74 =
* Resolved an ongoing issue with the compatibility of the plugin with servers running Imunify360. The file "wp-content/plugins/all-in-one-wp-migration/functions.php" was being falsely flagged and deleted due to a detection error. This update includes small changes to the "functions.php" file to modify its checksum and prevent the false flag by Imunify360
* Imunify360 has updated its signatures, which should prevent this issue from occurring on servers running the updated version. However, this plugin update serves as an additional measure to ensure that the issue is resolved for all users

= 7.73 =
* Better support for PHP 8.1

= 7.72 =
* Backups time based on selected WordPress time zone

= 7.71 =
* Removed the AI1WM_MAX_FILE_SIZE constant. This constant is no longer necessary

= 7.70 =
* Hooks that allow excluding specific database tables on export

= 7.69 =
* Improved support for custom backups location

= 7.68 =
* Better support for PHP 8.1

= 7.67 =
* Backups with a modified wp-content path cannot be downloaded

= 7.66 =
* Better support for WordPress v6.0.2

= 7.65 =
* Improved support for single-letter prefixed databases

= 7.64 =
* Better support for database binary fields

= 7.63 =
* CSRF and XSS issue in the plugin. Thank you, WPScan, for reporting it

= 7.62 =
* Password-protect and encrypt backups

= 7.61 =
* Issue with 7.60 release

= 7.60 =
* What's new page - easy way to get up to speed with the newest features
* List of all the items in a backup file, then select and download archived files
* Support for WordPress v6

= 7.59 =
* Fix a flaw in which the admin user has the ability to remove files other than backups

= 7.58 =
* Hide the backups count if there are no backups

= 7.57 =
* Improved UX on backups page

= 7.56 =
* Better support for PHP 8.1

= 7.55 =
* When importing or restoring across various PHP versions, the notice has been improved

= 7.54 =
* Improved YouTube and Twitter buttons

= 7.53 =
* Total number of backups in the plugin menu

= 7.52 =
* Out of disk space when exporting database.sql

= 7.51 =
* Link to YouTube Channel

= 7.50 =
* Improved reliability for scheduling events

= 7.49 =
* Better error handling when making HTTP requests
* Store a list of site files as CSV
* Filter to change the request method

= 7.48 =
* Support for BuddyPress plugin

= 7.47 =
* Improved database migration

= 7.46 =
* Support custom themes directory

= 7.45 =
* Support custom plugins directory

= 7.44 =
* Better support for MySQL <= 5.5
* Support for BuddyBoss plugin
* Report issue button

= 7.43 =
* Improved reliability

= 7.42 =
* Better support for WooCommerce plugin

= 7.41 =
* Improved free disk space checking
* Improved backup validation
* Improved path replacement on import
* Horizontal scrollbar on MacOS (Backups Page)

= 7.40 =
* Better support for WP Cerber plugin
* Backup page style issues on narrow screens

= 7.39 =
* Remove deprecated jQuery methods

= 5.56 =
* Fix an issue with WP_Hook class introcuded in WP 4.7

= 5.55 =
* Fix an issue with resolving URL on export/import when using non-blocking streams client

= 5.54 =
* Fix an issue with resolving URL on export/import

= 5.53 =
* Send HTTP basic authorization header on upload (fetch method)
* Add Accept-Encoding, Accept-Charset and Accept-Language on export/import
* Do not replace already replaced values on database import/export
* Set silent mode when activating sidewide plugins
* Replace old media style URLs with the new media style URLs on database import
* Replace user_level and capabilities user meta keys if tables have empty prefix on export
* Create separate action for extracting must-use plugins
* Add option "Do not export must-use plugins" in advanced settings
* Fix an issue with SSL that produces "Unable to resolve URL..."

= 5.52 =
* Simplify the text on import page
* Fix an issue with special characters on export and import
* Fix an issue with export and import of large files

= 5.51 =
* Add support for utf8mb4_unicode_520_ci database collation

= 5.50 =
* Improve database export process
* Simplify export and import cron
* Fix an issue with export and import progress status

= 5.49 =
* Test plugin up to WordPress 4.6

= 5.48 =
* Improve support for large databases on export
* Add support for Box cloud storage
* Fix an issue with status on export/import
* Fix an issue with asynchronous requests on export/import

= 5.47 =
* Fix an issue with incorrect file size on export

= 5.46 =
* Add "Restore from Backups" video in readme file
* Display message if backups are inaccessible

= 5.45 =
* Fix an issue with blogs.dir path replacement

= 5.44 =
* Add "Do not replace email domain" option in advanced settings
* Add "ai1wm_exclude_content_from_export" WordPress hook on export
* Add HTML5 uploader

= 5.43 =
* Fix an issue when archiving dynamic files on export
* Support custom upload path for multisites
* Add support for various cache plugins

= 5.42 =
* Catch E_PARSE error on mu-plugins import
* Fix an issue with stop export that doesn't clean up the storage directory
* Initialize new cache instead of flushing the existing one on import/export

= 5.41 =
* Fix an issue when replacing serialized values on import
* List files in chunks
* Convert svg images to png
* Check if backups are readable before displaying them on "Backups" page
* Display version incompatibility notification on export/import/restore screen
* Fix double port issue on Bitnami
* Fix an issue on multisite export with cloud extensions

= 5.40 =
* Test plugin up to WordPress 4.5

= 5.39 =
* Fix a bug in uploads path replacement

= 5.38 =
* Deactivate mu-plugins if fatal error appears on import

= 5.37 =
* Validate the archive before import

= 5.36 =
* Add OneDrive to readme.txt
* Fix a typo on import

= 5.35 =
* Add OneDrive to export/import pages
* Fix a bug when WordPress was used without a db prefix
* Fix a problem when downloading wpress files
* Improve the log system

= 4.19 =
* Fixed an issue with options cache

= 4.18 =
* Fixed an issue with large media files
* Fixed an issue with status file being cached

= 4.17 =
* Set "Tested up to" WordPress 4.4

= 4.16 =
* Fix an issue with the transport layer on export/import

= 4.15 =
* Fix an issue with resovling mechanism on export/import

= 4.14 =
* Fix an issue with database import

= 4.13 =
* Add new mechanism for resolving HTTP requests

= 4.12 =
* Fix an issue with Google Drive extension

= 4.11 =
* Fix content filters on export

= 4.10 =
* Add HTTPS URL replacement
* Fix an issue when PDO is not available

= 4.6 =
* Fix an issue when the plugin was getting stuck on "Done creating an empty archive"
* Fix an issue when the plugin was getting stuck during import

= 4.3 =
* Add URL extension support
* Filter "mu-plugins" directory if "Do not export plugins (files)" is checked
* Fix utf8mb4 issue
* Fix translation issue

= 4.2 =
* Fix .wpress.bin format

= 4.1 =
* Add port to the host header on export/import
* Rename .wpress file to .wpress.bin file

= 4.0 =
* Fix file permission checks

= 3.9 =
* Fix could not resolve domain name on export/import

= 3.8 =
* Fix undefined method on Backups page if PHP version is < 5.3.6

= 3.7 =
* Add IPv6 support on export/import

= 3.6 =
* Fixed undefined constant warnings

= 3.5 =
* Exclude core plugin and extensions on export if they have custom names

= 3.4 =
* Made export/import processes more reliable
* Allow the plugin to work with non-default name
* Preserve backups during plugin updates
* Improved find & replace functionality on the serialized data
* Removed backup file name restrictions

= 3.3 =
* Fixed a bug when retrieving export/import status progress
* Fixed a bug when database encoding utf8mb4_unicode_ci is not available

= 3.2.2 =
* Fixed plugin incompatibility during export/import that was reporting that the process could not be started

= 3.2.1 =
* Added username/password settings for WordPress sites behind HTTP basic authentication
* Fixed a bug when exporting/importing without public DNS record
* Fixed a bug when exporting/importing media files

= 3.2.0 =
* Added advanced settings on export page

= 3.1.1 =
* Fixed secret key issue on upgrade of the plugin

= 3.0.0 =
* Added export to File, [Dropbox](https://servmask.com/products/dropbox-extension), [Amazon S3](https://servmask.com/products/amazon-s3-extension), [Google Drive](https://servmask.com/products/google-drive-extension)
* Added import from File, [Dropbox](https://servmask.com/products/dropbox-extension), [Amazon S3](https://servmask.com/products/amazon-s3-extension), [Google Drive](https://servmask.com/products/google-drive-extension)
* Implemented our own archiving format that reduces export and import by a factor of 10
* One-click export with the new simplified export page
* Improved upload functionality with auto-recognizing chunk size on import
* New **Backups** page for storing all WordPress site exports
* Easy restore WordPress site from **Backups** page
* Monitoring availability of the disk space on the server
* Both export and import happen in time chunks of 3 seconds
* Plugin works behind HTTP basic authentication

= 2.0.4 =
* Updated readme to reflect that the plugin is not multisite compatible

= 2.0.3 =
* Fixed a security issue while importing site using regular users

= 2.0.2 =
* Added support for WordPress v4.0

= 2.0.1 =
* Fixed a bug when all user permissions are lost on import

= 2.0.0 =
* Added support for migration of WordPress in Network Mode (Multi Site)
* New improved UI and UX
* New improved language translations on the menu items and help texts
* Better error handling and notifications
* Fixed a bug while exporting comments and associated comments meta data
* Fixed a bug while using find/replace functionality
* Fixed a bug with storage directory permissions and search indexation

= 1.9.2 =
* Added PHP <= v5.2.7 compatibility

= 1.9.1 =
* Fixed an issue with earlier versions of PHP

= 1.9.0 =
* New improved design on the export/import page
* Added an option for gathering user experience statistics
* Added a message box with important notifications about the plugin
* Fixed a bug while exporting database with multiple WordPress sites
* Fixed a bug while exporting database with table constraints
* Fixed a bug with auto recognizing zip archiver

= 1.8.1 =
* Added "Get Support" link in the plugin list page
* Removed "All-in-One WP Migration Beta" link from the readme file

= 1.8.0 =
* Added support for dynamically recognizing Site URL and Home URL on the import page
* Fixed a bug when maximum uploaded size is exceeded
* Fixed a bug while exporting big database tables

= 1.7.2 =
* Added support for automatically switching database adapters for better performance and optimization
* Fixed a bug while using host:port syntax with MySQL PDO
* Fixed a bug while using find/replace functionality

= 1.7.1 =
* Fixed a bug while exporting WordPress plugins directory

= 1.7.0 =
* Added storage layer to avoid permission issues with OS's directory used for temporary storage
* Added additional checks to verify the consistency of the imported archive
* Fixed a bug that caused the database to be exported without data
* Removed unused variables from package.json file

= 1.6.0 =
* Added additional check for directory's permissions
* Added additional check for output buffering when exporting a file
* Fixed a bug when the archive was exported or imported with old version of Zlib library
* Fixed a bug with permalinks and flushing the rules

= 1.5.0 =
* Added support for additional errors and exceptions handling
* Added support for reporting a problem in better and easier way
* Improved support process in ZenDesk system for faster response time
* Fixed typos on the import page. Thanks to Terry Heenan

= 1.4.0 =
* Added a Twitter and Facebook share buttons to the sidebar on import and export pages

= 1.3.1 =
* Fixed a bug when the user was unable to import site archive
* Optimized and speeded up import process

= 1.3.0 =
* Added support for mysql connection to happen over sockets or TCP
* Added support for Windows OS and fully tested the plugin on IIS
* Added support for limited memory_limit - 1MB - The plugin now requires only 1MB to operate properly
* Added support for multisite
* Used mysql_unbuffered_query instead of mysql_query to overcome any memory problems
* Fixed a deprecated warning for mysql_pconnect when php 5.5 and above is used
* Fixed memory_limit problem with PCLZIP library
* Fixed a bug when the archive is exported with zero size when using PCLZIP
* Fixed a bug when the archive was exported broken on some servers
* Fixed a deprecated usage of preg_replace \e in php v5.5 and above

= 1.2.1 =
* Fixed an issue when HTTP Error was shown on some hosts after import, credit to Michael Simon
* Fixed an issue when exporting databases with different prefix than wp_, credit to najtrox
* Fixed an issue when PDO is avalable but mysql driver for PDO is not, credit to Jaydesain69
* Deleted a plugin specific option when uninstalling the plugin (clean after itself)
* Support is done via Zendesk
* Included WP Version and Plugin version in the feedback form

= 1.2.0 =
* Increased upload limit of files from 128MB to 512MB
* Used ZipArchive with fallback to PclZip (a few users notified us that they don't have ZipArchive enabled on their servers)
* Used PDO with fallback to mysql (a few users notified us that they dont have PDO enabled on their servers, mysql is deprecated as of PHP v5.5 but we are supporting PHP v5.2.17)
* Supported PHP v5.2.17 and WordPress v3.3 and above
* Fixed a bug during export that causes plugins to not be exported on some hosts (the problem that you are experiencing)

= 1.1.0 =
* Importing files using chunks to overcome any webserver upload size restriction
* Fixed a bug where HTTP code error was shown to some users

= 1.0.0 =
* Export database as SQL file
* Export media files
* Export themes files
* Export installed plugins
* Unlimited find/replace actions
* Option to exclude spam comments
* Option to apply find/replace to GUIDs
* Option to exclude post revisions
* Option to exclude tables data
