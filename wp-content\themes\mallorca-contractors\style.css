/*
Theme Name: Mallorca Contractors
Description: A minimalist, clean child theme of Twenty Twenty-Five for Mallorca home services contractors. Features professional design with JSON-based content management.
Author: Custom Development
Template: twentytwentyfive
Version: 1.0.0
License: GPL v2 or later
Text Domain: mallorca-contractors
*/

/* Import parent theme styles */
@import url("../twentytwentyfive/style.css");

/* ===================================
   EU COOKIE CONSENT STYLES
   =================================== */

/* Cookie consent banner */
.cookie-consent-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(44, 62, 80, 0.98);
    color: #ffffff;
    padding: 20px;
    z-index: 9999;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    transform: translateY(100%);
    transition: transform 0.3s ease-in-out;
}

.cookie-consent-banner.show {
    transform: translateY(0);
}

.cookie-consent-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
}

.cookie-consent-text {
    flex: 1;
    min-width: 300px;
}

.cookie-consent-text h4 {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.cookie-consent-text p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
    opacity: 0.9;
}

.cookie-consent-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.cookie-consent-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 100px;
}

.cookie-consent-btn.accept {
    background: #27ae60;
    color: #ffffff;
}

.cookie-consent-btn.accept:hover {
    background: #229954;
}

.cookie-consent-btn.decline {
    background: #e74c3c;
    color: #ffffff;
}

.cookie-consent-btn.decline:hover {
    background: #c0392b;
}

.cookie-consent-btn.settings {
    background: transparent;
    color: #ffffff;
    border: 1px solid #ffffff;
}

.cookie-consent-btn.settings:hover {
    background: #ffffff;
    color: #2c3e50;
}

/* Cookie settings modal */
.cookie-settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.cookie-settings-modal.show {
    display: flex;
}

.cookie-settings-content {
    background: #ffffff;
    border-radius: 8px;
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.cookie-settings-header {
    padding: 20px 20px 0 20px;
    border-bottom: 1px solid #eee;
}

.cookie-settings-header h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
}

.cookie-settings-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cookie-settings-body {
    padding: 20px;
}

.cookie-category {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.cookie-category:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.cookie-category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.cookie-category h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1rem;
}

.cookie-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.cookie-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.cookie-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.cookie-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

.cookie-toggle input:checked + .cookie-toggle-slider {
    background-color: #3498db;
}

.cookie-toggle input:checked + .cookie-toggle-slider:before {
    transform: translateX(26px);
}

.cookie-toggle input:disabled + .cookie-toggle-slider {
    background-color: #27ae60;
    cursor: not-allowed;
}

.cookie-category-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 10px;
}

.cookie-settings-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Responsive design */
@media (max-width: 768px) {
    .cookie-consent-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .cookie-consent-actions {
        justify-content: center;
    }

    .cookie-settings-content {
        margin: 10px;
        max-height: 90vh;
    }

    .cookie-settings-footer {
        flex-direction: column;
    }
}

/* Cookie notice in header (small indicator) */
.cookie-notice-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #3498db;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
    display: none;
}

.cookie-notice-indicator:hover {
    background: #2980b9;
}

.cookie-notice-indicator.show {
    display: block;
}

/* ===================================
   SERVICES PAGE HERO FIXES
   =================================== */

/* Ensure services hero has no top margin/padding */
.services-hero {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Fix cover block positioning */
.wp-block-cover.services-hero {
    position: relative;
    margin-top: 0;
    margin-bottom: 0;
}

/* Ensure proper hero image sizing */
.services-hero .wp-block-cover__image-background {
    object-fit: cover;
    width: 100%;
    height: 100%;
}

/* Fix any spacing issues from header */
.wp-site-blocks .services-hero {
    margin-top: 0 !important;
}

/* Ensure hero content is properly centered */
.services-hero .wp-block-cover__inner-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 500px;
    padding: 2rem;
}

/* Remove any default spacing that might cause gaps */
.services-hero + * {
    margin-top: 0;
}

/* Services Hero Section - Alternative approach */
.services-hero-section {
    min-height: 500px;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-size: cover !important;
    background-position: center center !important;
    position: relative;
    margin-top: 0 !important;
}

/* Ensure no spacing issues */
.wp-site-blocks .services-hero-section {
    margin-top: 0 !important;
}

/* Mobile optimization for background attachment */
@media (max-width: 768px) {
    .services-hero-section {
        background-attachment: scroll;
        min-height: 400px;
    }
}

/* ===================================
   HOMEPAGE HERO SECTION OPTIMIZATION
   =================================== */

/* Hero section background image optimization */
.hero-section .wp-block-cover__image-background {
    object-fit: cover;
    object-position: center center;
    width: 100%;
    height: 100%;
}

/* Ensure hero section has proper height and no overflow */
.hero-section .wp-block-cover {
    min-height: 350px;
    overflow: hidden;
}

/* Mobile optimization for hero */
@media (max-width: 768px) {
    .hero-section .wp-block-cover {
        min-height: 300px;
        padding-top: var(--wp--preset--spacing--50) !important;
        padding-bottom: var(--wp--preset--spacing--50) !important;
    }
}

/* Tablet optimization */
@media (max-width: 1024px) and (min-width: 769px) {
    .hero-section .wp-block-cover {
        min-height: 320px;
    }
}

/* ===================================
   HEADER HERO IMAGE OPTIMIZATION
   =================================== */

/* Header with background image */
.header-with-bg {
    background-image: url('/wp-content/uploads/mallorcahomeservices.webp');
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    position: relative;
}

/* Header overlay for better text readability */
.header-with-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.85);
    z-index: 1;
}

/* Ensure header content is above overlay */
.header-with-bg > * {
    position: relative;
    z-index: 2;
}

/* Reduce header height */
.header-with-bg .wp-block-group.alignwide {
    padding-top: var(--wp--preset--spacing--30) !important;
    padding-bottom: var(--wp--preset--spacing--30) !important;
}

/* Mobile header optimization */
@media (max-width: 768px) {
    .header-with-bg .wp-block-group.alignwide {
        padding-top: var(--wp--preset--spacing--20) !important;
        padding-bottom: var(--wp--preset--spacing--20) !important;
        flex-direction: column;
        gap: var(--wp--preset--spacing--20);
    }

    /* Stronger overlay on mobile for better readability */
    .header-with-bg::before {
        background-color: rgba(255, 255, 255, 0.9);
    }
}

/* Tablet header optimization */
@media (max-width: 1024px) and (min-width: 769px) {
    .header-with-bg .wp-block-group.alignwide {
        padding-top: var(--wp--preset--spacing--25) !important;
        padding-bottom: var(--wp--preset--spacing--25) !important;
    }
}

/* Professional Color Scheme for Contractors */
:root {
    --mc-primary: #2c3e50;      /* Professional dark blue */
    --mc-secondary: #3498db;     /* Trust blue */
    --mc-accent: #e67e22;        /* Warm orange */
    --mc-success: #27ae60;       /* Success green */
    --mc-light: #ecf0f1;         /* Light gray */
    --mc-dark: #34495e;          /* Dark gray */
    --mc-white: #ffffff;         /* Pure white */
    --mc-text: #2c3e50;          /* Text color */
    --mc-text-light: #7f8c8d;    /* Light text */
}

/* Global Styles */
body {
    font-family: 'Manrope', sans-serif;
    color: var(--mc-text);
    line-height: 1.6;
}

/* Header Styles */
.site-header {
    background: var(--mc-white);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.site-header .wp-block-navigation {
    padding: 1rem 0;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--mc-primary) 0%, var(--mc-secondary) 100%);
    color: var(--mc-white);
    padding: 4rem 0;
    text-align: center;
}

.hero-section h1 {
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-section p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Modular Vertical Blocks */
.vertical-block {
    padding: 3rem 0;
    border-bottom: 1px solid var(--mc-light);
}

.vertical-block:last-child {
    border-bottom: none;
}

.vertical-block--primary {
    background: var(--mc-white);
}

.vertical-block--secondary {
    background: var(--mc-light);
}

.vertical-block--accent {
    background: var(--mc-primary);
    color: var(--mc-white);
}

/* Service Cards */
.service-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.service-card {
    background: var(--mc-white);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.service-card h3 {
    color: var(--mc-primary);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.service-card .service-icon {
    width: 60px;
    height: 60px;
    background: var(--mc-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: var(--mc-white);
    font-size: 1.5rem;
}

/* Buttons */
.btn-primary {
    background: var(--mc-accent);
    color: var(--mc-white);
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    display: inline-block;
    transition: background 0.3s ease;
    font-weight: 600;
}

.btn-primary:hover {
    background: #d35400;
    color: var(--mc-white);
}

.btn-secondary {
    background: transparent;
    color: var(--mc-primary);
    padding: 0.75rem 2rem;
    border: 2px solid var(--mc-primary);
    border-radius: 5px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-weight: 600;
}

.btn-secondary:hover {
    background: var(--mc-primary);
    color: var(--mc-white);
}

/* Footer */
.site-footer {
    background: var(--mc-primary);
    color: var(--mc-white);
    padding: 3rem 0 1rem;
}

.footer-services {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-services h4 {
    color: var(--mc-accent);
    margin-bottom: 1rem;
}

.footer-services a {
    color: var(--mc-white);
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-services a:hover {
    opacity: 1;
    text-decoration: underline;
}

/* Contact Information */
.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.contact-item {
    text-align: center;
    padding: 2rem;
    background: var(--mc-light);
    border-radius: 8px;
}

.contact-item .contact-icon {
    width: 50px;
    height: 50px;
    background: var(--mc-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: var(--mc-white);
    font-size: 1.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 0;
    }
    
    .service-cards {
        grid-template-columns: 1fr;
    }
    
    .vertical-block {
        padding: 2rem 0;
    }
    
    .contact-info {
        grid-template-columns: 1fr;
    }
}

/* Mega Menu Styles */
.mega-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--mc-white);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 2rem;
    display: none;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.wp-block-navigation-item:hover .mega-menu {
    display: grid;
}

.mega-menu-section h4 {
    color: var(--mc-primary);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.mega-menu-section a {
    display: block;
    padding: 0.5rem 0;
    color: var(--mc-text);
    text-decoration: none;
    transition: color 0.3s ease;
}

.mega-menu-section a:hover {
    color: var(--mc-secondary);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
