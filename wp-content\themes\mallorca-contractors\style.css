/*
Theme Name: Mallorca Contractors
Description: A minimalist, clean child theme of Twenty Twenty-Five for Mallorca home services contractors. Features professional design with JSON-based content management.
Author: Custom Development
Template: twentytwentyfive
Version: 1.0.0
License: GPL v2 or later
Text Domain: mallorca-contractors
*/

/* Import parent theme styles */
@import url("../twentytwentyfive/style.css");

/* ===================================
   CRITICAL MOBILE OVERFLOW PREVENTION
   =================================== */

/* Prevent horizontal scroll on all devices */
html {
    overflow-x: hidden !important;
    max-width: 100vw !important;
}

body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    position: relative !important;
}

/* Global box-sizing fix */
*, *::before, *::after {
    box-sizing: border-box !important;
}

/* WordPress block overflow prevention */
.wp-block-group,
.wp-block-columns,
.wp-block-column,
.wp-block-cover,
.alignfull,
.alignwide {
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
}

/* Fix WordPress columns with inline styles */
.wp-block-columns[style] {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Mobile-specific overflow fixes */
@media (max-width: 768px) {
    .wp-block-columns,
    .wp-block-column {
        width: 100% !important;
        max-width: 100% !important;
        flex-basis: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}

/* Professional Color Scheme for Contractors */
:root {
    --mc-primary: #2c3e50;      /* Professional dark blue */
    --mc-secondary: #3498db;     /* Trust blue */
    --mc-accent: #e67e22;        /* Warm orange */
    --mc-success: #27ae60;       /* Success green */
    --mc-light: #ecf0f1;         /* Light gray */
    --mc-dark: #34495e;          /* Dark gray */
    --mc-white: #ffffff;         /* Pure white */
    --mc-text: #2c3e50;          /* Text color */
    --mc-text-light: #7f8c8d;    /* Light text */
}

/* Global Styles */
body {
    font-family: 'Manrope', sans-serif;
    color: var(--mc-text);
    line-height: 1.6;
    overflow-x: hidden; /* Prevent horizontal scroll */
}

/* Prevent horizontal overflow globally */
* {
    box-sizing: border-box;
}

html {
    overflow-x: hidden;
}

/* Fix container overflow issues */
.wp-block-group,
.wp-block-columns,
.wp-block-column {
    max-width: 100%;
    overflow-x: hidden;
}

/* Header Styles */
.site-header {
    background: var(--mc-white);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.site-header .wp-block-navigation {
    padding: 1rem 0;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--mc-primary) 0%, var(--mc-secondary) 100%);
    color: var(--mc-white);
    padding: 4rem 0;
    text-align: center;
}

.hero-section h1 {
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-section p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Modular Vertical Blocks */
.vertical-block {
    padding: 3rem 0;
    border-bottom: 1px solid var(--mc-light);
}

.vertical-block:last-child {
    border-bottom: none;
}

.vertical-block--primary {
    background: var(--mc-white);
}

.vertical-block--secondary {
    background: var(--mc-light);
}

.vertical-block--accent {
    background: var(--mc-primary);
    color: var(--mc-white);
}

/* Service Cards */
.service-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.service-card {
    background: var(--mc-white);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.service-card h3 {
    color: var(--mc-primary);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.service-card .service-icon {
    width: 60px;
    height: 60px;
    background: var(--mc-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: var(--mc-white);
    font-size: 1.5rem;
}

/* Buttons */
.btn-primary {
    background: var(--mc-accent);
    color: var(--mc-white);
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    display: inline-block;
    transition: background 0.3s ease;
    font-weight: 600;
}

.btn-primary:hover {
    background: #d35400;
    color: var(--mc-white);
}

.btn-secondary {
    background: transparent;
    color: var(--mc-primary);
    padding: 0.75rem 2rem;
    border: 2px solid var(--mc-primary);
    border-radius: 5px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-weight: 600;
}

.btn-secondary:hover {
    background: var(--mc-primary);
    color: var(--mc-white);
}

/* Footer */
.site-footer {
    background: var(--mc-primary);
    color: var(--mc-white);
    padding: 3rem 0 1rem;
}

.footer-services {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-services h4 {
    color: var(--mc-accent);
    margin-bottom: 1rem;
}

.footer-services a {
    color: var(--mc-white);
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-services a:hover {
    opacity: 1;
    text-decoration: underline;
}

/* Contact Information */
.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.contact-item {
    text-align: center;
    padding: 2rem;
    background: var(--mc-light);
    border-radius: 8px;
}

.contact-item .contact-icon {
    width: 50px;
    height: 50px;
    background: var(--mc-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: var(--mc-white);
    font-size: 1.25rem;
}

/* ===================================
   COMPREHENSIVE MOBILE OPTIMIZATION
   =================================== */

/* Mobile-First Responsive Design */
@media (max-width: 480px) {
    /* Ultra-small screens (phones in portrait) */

    /* Global mobile adjustments */
    body {
        font-size: 14px;
        line-height: 1.5;
        overflow-x: hidden !important;
        width: 100% !important;
        max-width: 100vw !important;
    }

    /* Fix all containers to prevent overflow */
    .wp-block-group,
    .wp-block-columns,
    .wp-block-column,
    .wp-block-cover,
    .alignfull,
    .alignwide {
        max-width: 100vw !important;
        overflow-x: hidden !important;
        box-sizing: border-box !important;
    }

    /* Fix specific WordPress block issues */
    .wp-block-columns {
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-left: var(--wp--preset--spacing--20) !important;
        padding-right: var(--wp--preset--spacing--20) !important;
    }

    /* Fix alignfull elements */
    .alignfull {
        margin-left: calc(-1 * var(--wp--preset--spacing--20)) !important;
        margin-right: calc(-1 * var(--wp--preset--spacing--20)) !important;
        width: calc(100vw - 0px) !important;
        max-width: calc(100vw - 0px) !important;
    }

    /* Header mobile optimization */
    .site-header {
        width: 100% !important;
        max-width: 100vw !important;
        overflow-x: hidden !important;
    }

    .site-header .wp-block-group.alignwide {
        flex-direction: column !important;
        gap: var(--wp--preset--spacing--20) !important;
        padding: var(--wp--preset--spacing--20) !important;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        overflow-x: hidden !important;
    }

    .site-header .wp-block-group {
        flex-wrap: wrap !important;
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
    }

    .site-header .has-primary-background-color {
        padding: var(--wp--preset--spacing--20) var(--wp--preset--spacing--30) !important;
    }

    .site-header .has-primary-background-color a {
        font-size: 1.1rem !important;
    }

    .site-header .has-text-light-color {
        display: none; /* Hide tagline on very small screens */
    }

    .site-header .has-light-gray-background-color {
        padding: var(--wp--preset--spacing--10) var(--wp--preset--spacing--20) !important;
    }

    .site-header .wp-block-button__link {
        padding: var(--wp--preset--spacing--20) var(--wp--preset--spacing--30) !important;
        font-size: 0.75rem !important;
    }

    /* Hero section mobile */
    .hero-section .wp-block-cover {
        min-height: 400px !important;
        padding: var(--wp--preset--spacing--40) var(--wp--preset--spacing--20) !important;
    }

    .hero-section h1 {
        font-size: clamp(1.5rem, 8vw, 2.5rem) !important;
        margin-bottom: var(--wp--preset--spacing--30) !important;
    }

    .hero-section p {
        font-size: 1rem !important;
        margin-bottom: var(--wp--preset--spacing--40) !important;
    }

    .hero-section .wp-block-buttons {
        flex-direction: column !important;
        gap: var(--wp--preset--spacing--20) !important;
    }

    .hero-section .wp-block-button__link {
        padding: var(--wp--preset--spacing--30) var(--wp--preset--spacing--40) !important;
        font-size: 1rem !important;
        text-align: center !important;
        width: 100% !important;
        display: block !important;
    }

    /* Service cards mobile */
    .wp-block-columns {
        flex-direction: column !important;
        gap: var(--wp--preset--spacing--30) !important;
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
        overflow-x: hidden !important;
    }

    .wp-block-column {
        width: 100% !important;
        max-width: 100% !important;
        flex-basis: 100% !important;
        margin: 0 !important;
        padding: 0 var(--wp--preset--spacing--20) !important;
    }

    .service-card {
        padding: var(--wp--preset--spacing--30) !important;
        margin: 0 0 var(--wp--preset--spacing--20) 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }

    .service-card h3 {
        font-size: 1.25rem !important;
    }

    .service-card p {
        font-size: 0.875rem !important;
    }

    /* Navigation mobile */
    .wp-block-navigation {
        font-size: 0.875rem !important;
    }

    .wp-block-navigation .wp-block-navigation__submenu-container {
        position: static !important;
        box-shadow: none !important;
        background: transparent !important;
    }

    /* Vertical blocks mobile */
    .vertical-block {
        padding: var(--wp--preset--spacing--40) var(--wp--preset--spacing--20) !important;
    }

    /* Contact info mobile */
    .contact-info {
        grid-template-columns: 1fr !important;
        gap: var(--wp--preset--spacing--20) !important;
    }

    .contact-item {
        padding: var(--wp--preset--spacing--30) !important;
    }
}

@media (max-width: 768px) {
    /* Tablets and small laptops */

    /* Global overflow prevention */
    body, html {
        overflow-x: hidden !important;
        max-width: 100vw !important;
    }

    /* Fix all containers */
    .wp-block-group,
    .wp-block-columns,
    .wp-block-column,
    .alignfull,
    .alignwide {
        max-width: 100vw !important;
        overflow-x: hidden !important;
        box-sizing: border-box !important;
    }

    /* Header tablet optimization */
    .site-header {
        width: 100% !important;
        max-width: 100vw !important;
        overflow-x: hidden !important;
    }

    .site-header .wp-block-group.alignwide {
        flex-wrap: wrap !important;
        gap: var(--wp--preset--spacing--30) !important;
        width: 100% !important;
        max-width: 100% !important;
        padding-left: var(--wp--preset--spacing--20) !important;
        padding-right: var(--wp--preset--spacing--20) !important;
        overflow-x: hidden !important;
    }

    .site-header .has-text-light-color {
        margin-left: 0 !important;
        margin-top: var(--wp--preset--spacing--10) !important;
    }

    /* Hero section tablet */
    .hero-section .wp-block-cover {
        min-height: 450px !important;
        padding: var(--wp--preset--spacing--60) var(--wp--preset--spacing--30) !important;
    }

    .hero-section h1 {
        font-size: clamp(2rem, 6vw, 3rem) !important;
    }

    .hero-section .wp-block-buttons {
        flex-direction: column !important;
        align-items: center !important;
        gap: var(--wp--preset--spacing--30) !important;
    }

    /* Service cards tablet */
    .wp-block-columns {
        flex-direction: column !important;
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
        overflow-x: hidden !important;
    }

    .wp-block-column {
        width: 100% !important;
        max-width: 100% !important;
        flex-basis: 100% !important;
        margin: 0 !important;
        padding: 0 var(--wp--preset--spacing--20) !important;
    }

    .service-card {
        max-width: 100% !important;
        width: 100% !important;
        margin: 0 0 var(--wp--preset--spacing--30) 0 !important;
        box-sizing: border-box !important;
    }

    /* Navigation tablet */
    .wp-block-navigation {
        flex-wrap: wrap !important;
        justify-content: center !important;
    }

    /* Vertical blocks tablet */
    .vertical-block {
        padding: var(--wp--preset--spacing--60) var(--wp--preset--spacing--30) !important;
    }

    /* Contact info tablet */
    .contact-info {
        grid-template-columns: 1fr !important;
        max-width: 600px !important;
        margin: 0 auto !important;
    }
}

@media (max-width: 1024px) {
    /* Small laptops and large tablets */

    /* Service cards small laptop */
    .wp-block-columns {
        flex-direction: column !important;
    }

    .service-card {
        max-width: 600px !important;
        margin: 0 auto var(--wp--preset--spacing--40) auto !important;
    }

    /* Navigation small laptop */
    .wp-block-navigation {
        flex-wrap: wrap !important;
        gap: var(--wp--preset--spacing--40) !important;
    }
}

/* Mega Menu Styles */
.mega-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--mc-white);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 2rem;
    display: none;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.wp-block-navigation-item:hover .mega-menu {
    display: grid;
}

.mega-menu-section h4 {
    color: var(--mc-primary);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.mega-menu-section a {
    display: block;
    padding: 0.5rem 0;
    color: var(--mc-text);
    text-decoration: none;
    transition: color 0.3s ease;
}

.mega-menu-section a:hover {
    color: var(--mc-secondary);
}

/* ===================================
   MOBILE-SPECIFIC IMPROVEMENTS
   =================================== */

/* Touch-friendly buttons */
@media (max-width: 768px) {
    .wp-block-button__link {
        min-height: 44px !important;
        min-width: 44px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        touch-action: manipulation !important;
    }
}

/* Improved text readability on mobile */
@media (max-width: 480px) {
    h1, h2, h3, h4, h5, h6 {
        line-height: 1.3 !important;
        word-wrap: break-word !important;
    }

    p {
        line-height: 1.6 !important;
        word-wrap: break-word !important;
    }
}

/* Mobile navigation improvements */
@media (max-width: 768px) {
    .wp-block-navigation__responsive-container.is-menu-open {
        padding: var(--wp--preset--spacing--30) !important;
    }

    .wp-block-navigation__responsive-container-content {
        padding: var(--wp--preset--spacing--20) !important;
    }

    .wp-block-navigation-item {
        margin: var(--wp--preset--spacing--10) 0 !important;
    }

    .wp-block-navigation-item a {
        padding: var(--wp--preset--spacing--20) !important;
        display: block !important;
        border-radius: 4px !important;
        transition: background-color 0.3s ease !important;
    }

    .wp-block-navigation-item a:hover {
        background-color: var(--wp--preset--color--light-gray) !important;
    }
}

/* Mobile form improvements */
@media (max-width: 768px) {
    .wp-block-group.has-light-gray-background-color {
        padding: var(--wp--preset--spacing--30) !important;
    }

    input, textarea, select {
        font-size: 16px !important; /* Prevents zoom on iOS */
        padding: var(--wp--preset--spacing--20) !important;
        border-radius: 4px !important;
    }
}

/* Mobile spacing improvements */
@media (max-width: 480px) {
    .wp-block-group.alignwide {
        padding-left: var(--wp--preset--spacing--20) !important;
        padding-right: var(--wp--preset--spacing--20) !important;
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        overflow-x: hidden !important;
    }

    .wp-block-group.alignfull {
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100vw !important;
        max-width: 100vw !important;
        overflow-x: hidden !important;
    }

    /* Fix WordPress block margins that cause overflow */
    .wp-block-columns[style*="margin-left"] {
        margin-left: 0 !important;
    }

    .wp-block-columns[style*="margin-right"] {
        margin-right: 0 !important;
    }

    /* Remove problematic inline styles */
    .wp-block-columns[style*="margin-top"] {
        margin-top: var(--wp--preset--spacing--30) !important;
    }
}

@media (max-width: 768px) {
    /* Fix WordPress block spacing issues */
    .wp-block-columns[style*="margin-left"] {
        margin-left: 0 !important;
    }

    .wp-block-columns[style*="margin-right"] {
        margin-right: 0 !important;
    }

    /* Ensure all blocks fit within viewport */
    .wp-block-group,
    .wp-block-columns,
    .wp-block-column {
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
        box-sizing: border-box !important;
    }
}

/* Mobile image optimization */
@media (max-width: 768px) {
    img {
        height: auto !important;
        max-width: 100% !important;
    }

    .wp-block-image {
        margin: var(--wp--preset--spacing--20) 0 !important;
    }
}

/* Mobile footer improvements */
@media (max-width: 768px) {
    .site-footer {
        padding: var(--wp--preset--spacing--40) var(--wp--preset--spacing--20) !important;
        text-align: center !important;
    }

    .footer-services {
        grid-template-columns: 1fr !important;
        gap: var(--wp--preset--spacing--30) !important;
        text-align: center !important;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }

/* Mobile utility classes */
@media (max-width: 768px) {
    .mobile-hidden { display: none !important; }
    .mobile-center { text-align: center !important; }
    .mobile-full-width { width: 100% !important; }
    .mobile-no-margin { margin: 0 !important; }
    .mobile-small-padding { padding: var(--wp--preset--spacing--20) !important; }
}

/* ===================================
   SERVICE PAGES MOBILE OPTIMIZATION
   =================================== */

/* Service page hero sections */
@media (max-width: 480px) {
    .wp-block-cover {
        min-height: 300px !important;
        padding: var(--wp--preset--spacing--40) var(--wp--preset--spacing--20) !important;
    }

    .wp-block-cover h1 {
        font-size: clamp(1.8rem, 8vw, 2.5rem) !important;
    }

    .wp-block-cover p {
        font-size: 1rem !important;
    }
}

@media (max-width: 768px) {
    .wp-block-cover {
        min-height: 350px !important;
        padding: var(--wp--preset--spacing--50) var(--wp--preset--spacing--30) !important;
    }

    /* Service page columns mobile */
    .wp-block-columns {
        flex-direction: column !important;
        gap: var(--wp--preset--spacing--40) !important;
    }

    .wp-block-column {
        flex-basis: 100% !important;
        width: 100% !important;
    }

    /* Service details sidebar mobile */
    .wp-block-column .wp-block-group.has-primary-background-color {
        margin-top: var(--wp--preset--spacing--40) !important;
        padding: var(--wp--preset--spacing--30) !important;
    }

    /* Process steps mobile */
    .wp-block-columns .wp-block-group.has-base-background-color {
        padding: var(--wp--preset--spacing--30) !important;
        margin-bottom: var(--wp--preset--spacing--20) !important;
    }

    /* Service page buttons mobile */
    .wp-block-buttons {
        flex-direction: column !important;
        align-items: center !important;
        gap: var(--wp--preset--spacing--20) !important;
    }

    .wp-block-button__link {
        width: 100% !important;
        max-width: 300px !important;
        text-align: center !important;
        display: block !important;
    }
}

/* ===================================
   CONTACT PAGE MOBILE OPTIMIZATION
   =================================== */

@media (max-width: 768px) {
    /* Contact form mobile */
    .mallorca-contact-form .form-group {
        margin-bottom: var(--wp--preset--spacing--20) !important;
    }

    .mallorca-contact-form input,
    .mallorca-contact-form textarea {
        font-size: 16px !important; /* Prevents zoom on iOS */
        padding: var(--wp--preset--spacing--20) !important;
    }

    .mallorca-contact-form .submit-btn {
        width: 100% !important;
        padding: var(--wp--preset--spacing--30) !important;
        font-size: 1rem !important;
    }
}

/* ===================================
   ABOUT PAGE MOBILE OPTIMIZATION
   =================================== */

@media (max-width: 768px) {
    /* About page content mobile */
    .wp-block-group.alignwide {
        padding-left: var(--wp--preset--spacing--20) !important;
        padding-right: var(--wp--preset--spacing--20) !important;
    }

    /* About page lists mobile */
    .wp-block-list {
        padding-left: var(--wp--preset--spacing--30) !important;
    }

    .wp-block-list li {
        margin-bottom: var(--wp--preset--spacing--10) !important;
    }
}

/* ===================================
   MOBILE PERFORMANCE & ACCESSIBILITY
   =================================== */

/* Touch device optimizations */
.touch-device .wp-block-button__link {
    min-height: 44px !important;
    min-width: 44px !important;
}

.touch-device a,
.touch-device button {
    min-height: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Improved focus states for mobile */
@media (max-width: 768px) {
    a:focus,
    button:focus,
    input:focus,
    textarea:focus {
        outline: 3px solid var(--wp--preset--color--primary) !important;
        outline-offset: 2px !important;
    }

    /* Better text selection on mobile */
    ::selection {
        background-color: var(--wp--preset--color--accent) !important;
        color: var(--wp--preset--color--base) !important;
    }
}

/* Mobile loading optimizations */
@media (max-width: 768px) {
    img {
        loading: lazy;
    }

    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
}

/* Mobile print styles */
@media print {
    .site-header,
    .wp-block-navigation,
    .wp-block-buttons,
    .mobile-hidden {
        display: none !important;
    }

    body {
        font-size: 12pt !important;
        line-height: 1.4 !important;
    }

    h1, h2, h3 {
        page-break-after: avoid !important;
    }
}

/* ===================================
   FINAL OVERFLOW PREVENTION FIXES
   =================================== */

/* Force all elements to respect viewport width */
@media (max-width: 768px) {
    * {
        max-width: 100vw !important;
        overflow-x: hidden !important;
    }

    /* Specific fixes for WordPress blocks */
    .wp-block-columns,
    .wp-block-column,
    .wp-block-group {
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-left: var(--wp--preset--spacing--20) !important;
        padding-right: var(--wp--preset--spacing--20) !important;
        box-sizing: border-box !important;
    }

    /* Override any problematic inline styles */
    .wp-block-columns[style*="margin-left"] {
        margin-left: 0 !important;
    }

    .wp-block-columns[style*="margin-right"] {
        margin-right: 0 !important;
    }

    /* Ensure alignfull elements don't overflow */
    .alignfull {
        width: 100vw !important;
        max-width: 100vw !important;
        margin-left: calc(-1 * var(--wp--preset--spacing--20)) !important;
        margin-right: calc(-1 * var(--wp--preset--spacing--20)) !important;
        padding-left: var(--wp--preset--spacing--20) !important;
        padding-right: var(--wp--preset--spacing--20) !important;
    }

    /* Fix service cards specifically */
    .service-card {
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        box-sizing: border-box !important;
    }
}
