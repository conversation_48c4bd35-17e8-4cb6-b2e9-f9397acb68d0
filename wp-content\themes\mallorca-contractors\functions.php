<?php
/**
 * Mallorca Contractors Child Theme Functions
 * 
 * @package Mallorca_Contractors
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue parent and child theme styles
 */
function mallorca_contractors_enqueue_styles() {
    // Enqueue parent theme style
    wp_enqueue_style(
        'twentytwentyfive-style',
        get_template_directory_uri() . '/style.css',
        array(),
        wp_get_theme()->get('Version')
    );
    
    // Enqueue child theme style
    wp_enqueue_style(
        'mallorca-contractors-style',
        get_stylesheet_directory_uri() . '/style.css',
        array('twentytwentyfive-style'),
        wp_get_theme()->get('Version')
    );
}
add_action('wp_enqueue_scripts', 'mallorca_contractors_enqueue_styles');

/**
 * JSON Data Management Functions
 */

/**
 * Get JSON data from the external file
 * 
 * @return array|false JSON data or false on failure
 */
function mc_get_json_data() {
    static $json_data = null;
    
    if ($json_data === null) {
        $json_file_path = 'H:/VSC/MallorcaHomeServices/mallorcahomeservices-augment.json';
        
        if (file_exists($json_file_path)) {
            $json_content = file_get_contents($json_file_path);
            $json_data = json_decode($json_content, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log('JSON decode error: ' . json_last_error_msg());
                $json_data = false;
            }
        } else {
            error_log('JSON file not found: ' . $json_file_path);
            $json_data = false;
        }
    }
    
    return $json_data;
}

/**
 * Get homepage hero content
 * 
 * @return array Hero content data
 */
function mc_get_hero_content() {
    $data = mc_get_json_data();
    
    if ($data && isset($data['homepage'])) {
        return $data['homepage'];
    }
    
    // Fallback content
    return array(
        'title' => 'Professional Home Services in Mallorca',
        'subtitle' => 'Quality craftsmanship and reliable service for all your home improvement needs',
        'cta_text' => 'Get Free Quote',
        'cta_link' => '/contact'
    );
}

/**
 * Get About Us content
 * 
 * @return array About Us content data
 */
function mc_get_about_content() {
    $data = mc_get_json_data();
    
    if ($data && isset($data['about'])) {
        return $data['about'];
    }
    
    // Fallback content
    return array(
        'title' => 'About Mallorca Contractors',
        'content' => 'We are dedicated professionals providing quality home services across Mallorca.',
        'experience' => '10+ Years Experience',
        'projects' => '500+ Completed Projects'
    );
}

/**
 * Get all services
 * 
 * @return array Services data
 */
function mc_get_services() {
    $data = mc_get_json_data();
    
    if ($data && isset($data['services'])) {
        return $data['services'];
    }
    
    // Fallback services
    return array(
        array(
            'id' => 'general-renovation',
            'name' => 'General Renovation',
            'description' => 'Complete home renovation services',
            'icon' => '🏠',
            'tasks' => array('Planning', 'Execution', 'Finishing'),
            'benefits' => array('Increased home value', 'Modern updates'),
            'timeframe' => '2-8 weeks'
        ),
        array(
            'id' => 'plumbing',
            'name' => 'Plumbing',
            'description' => 'Professional plumbing installation and repair',
            'icon' => '🔧',
            'tasks' => array('Installation', 'Repair', 'Maintenance'),
            'benefits' => array('Reliable water systems', 'Emergency repairs'),
            'timeframe' => '1-3 days'
        ),
        array(
            'id' => 'electrical',
            'name' => 'Electrical',
            'description' => 'Safe and certified electrical work',
            'icon' => '⚡',
            'tasks' => array('Wiring', 'Installation', 'Safety checks'),
            'benefits' => array('Safe electrical systems', 'Energy efficiency'),
            'timeframe' => '1-5 days'
        ),
        array(
            'id' => 'painting',
            'name' => 'Painting',
            'description' => 'Interior and exterior painting services',
            'icon' => '🎨',
            'tasks' => array('Preparation', 'Priming', 'Painting'),
            'benefits' => array('Fresh appearance', 'Protection'),
            'timeframe' => '2-7 days'
        ),
        array(
            'id' => 'carpentry',
            'name' => 'Carpentry',
            'description' => 'Custom woodwork and carpentry',
            'icon' => '🔨',
            'tasks' => array('Custom builds', 'Repairs', 'Installation'),
            'benefits' => array('Custom solutions', 'Quality craftsmanship'),
            'timeframe' => '1-4 weeks'
        ),
        array(
            'id' => 'bathroom-kitchen',
            'name' => 'Bathroom & Kitchen',
            'description' => 'Complete bathroom and kitchen renovations',
            'icon' => '🚿',
            'tasks' => array('Design', 'Installation', 'Finishing'),
            'benefits' => array('Modern functionality', 'Increased value'),
            'timeframe' => '2-6 weeks'
        )
    );
}

/**
 * Get specific service by ID
 * 
 * @param string $service_id Service identifier
 * @return array|false Service data or false if not found
 */
function mc_get_service($service_id) {
    $services = mc_get_services();

    foreach ($services as $service) {
        if ($service['id'] === $service_id) {
            return $service;
        }
    }

    return false;
}

/**
 * Get service image data
 *
 * @param string $service_id Service identifier
 * @return array|false Image data or false if not found
 */
function mc_get_service_image($service_id) {
    $json_data = mc_get_json_data();

    if (!$json_data || !isset($json_data['services'])) {
        return false;
    }

    foreach ($json_data['services'] as $service) {
        if (isset($service['serviceId']) && $service['serviceId'] == $service_id) {
            return isset($service['image']) ? $service['image'] : false;
        }
    }

    return false;
}

/**
 * Get all service images
 *
 * @return array Array of service images keyed by service ID
 */
function mc_get_all_service_images() {
    $json_data = mc_get_json_data();
    $images = array();

    if (!$json_data || !isset($json_data['services'])) {
        return $images;
    }

    foreach ($json_data['services'] as $service) {
        if (isset($service['image']) && isset($service['serviceId'])) {
            $images[$service['serviceId']] = $service['image'];
        }
    }

    return $images;
}

/**
 * Get contact information
 * 
 * @return array Contact data
 */
function mc_get_contact_info() {
    $data = mc_get_json_data();
    
    if ($data && isset($data['contact'])) {
        return $data['contact'];
    }
    
    // Fallback contact info
    return array(
        'email' => '<EMAIL>',
        'phone' => '+34 ***********',
        'address' => 'Mallorca, Spain',
        'hours' => 'Mon-Fri: 8:00-18:00'
    );
}

/**
 * Register custom post types and taxonomies
 */
function mc_register_post_types() {
    // Register Projects post type for portfolio
    register_post_type('mc_project', array(
        'labels' => array(
            'name' => 'Projects',
            'singular_name' => 'Project',
            'add_new' => 'Add New Project',
            'add_new_item' => 'Add New Project',
            'edit_item' => 'Edit Project',
            'new_item' => 'New Project',
            'view_item' => 'View Project',
            'search_items' => 'Search Projects',
            'not_found' => 'No projects found',
            'not_found_in_trash' => 'No projects found in trash'
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'menu_icon' => 'dashicons-hammer',
        'rewrite' => array('slug' => 'projects')
    ));
}
add_action('init', 'mc_register_post_types');

/**
 * Add theme support
 */
function mc_theme_support() {
    // Add support for post thumbnails
    add_theme_support('post-thumbnails');
    
    // Add support for custom logo
    add_theme_support('custom-logo', array(
        'height' => 60,
        'width' => 200,
        'flex-height' => true,
        'flex-width' => true
    ));
    
    // Add support for HTML5 markup
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption'
    ));
}
add_action('after_setup_theme', 'mc_theme_support');

/**
 * Register navigation menus
 */
function mc_register_menus() {
    register_nav_menus(array(
        'primary' => 'Primary Navigation',
        'services' => 'Services Menu',
        'footer' => 'Footer Menu'
    ));
}
add_action('init', 'mc_register_menus');

/**
 * Enqueue custom scripts
 */
function mc_enqueue_scripts() {
    wp_enqueue_script(
        'mallorca-contractors-scripts',
        get_stylesheet_directory_uri() . '/assets/js/scripts.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );
}
add_action('wp_enqueue_scripts', 'mc_enqueue_scripts');

/**
 * Add custom body classes
 */
function mc_body_classes($classes) {
    $classes[] = 'mallorca-contractors';

    if (is_front_page()) {
        $classes[] = 'homepage';
    }

    return $classes;
}
add_filter('body_class', 'mc_body_classes');

/**
 * Create custom shortcodes for JSON content
 */

/**
 * Hero section shortcode
 */
function mc_hero_shortcode($atts) {
    $hero_content = mc_get_hero_content();

    ob_start();
    ?>
    <div class="hero-section">
        <div class="hero-content">
            <h1><?php echo esc_html($hero_content['title']); ?></h1>
            <p><?php echo esc_html($hero_content['subtitle']); ?></p>
            <a href="<?php echo esc_url($hero_content['cta_link']); ?>" class="btn-primary">
                <?php echo esc_html($hero_content['cta_text']); ?>
            </a>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('mc_hero', 'mc_hero_shortcode');

/**
 * Services grid shortcode
 */
function mc_services_grid_shortcode($atts) {
    $atts = shortcode_atts(array(
        'limit' => 6,
        'columns' => 3
    ), $atts);

    $services = mc_get_services();
    $services = array_slice($services, 0, intval($atts['limit']));

    ob_start();
    ?>
    <div class="service-cards" style="grid-template-columns: repeat(<?php echo intval($atts['columns']); ?>, 1fr);">
        <?php foreach ($services as $service): ?>
            <div class="service-card">
                <div class="service-icon"><?php echo esc_html($service['icon']); ?></div>
                <h3><?php echo esc_html($service['name']); ?></h3>
                <p><?php echo esc_html($service['description']); ?></p>
                <p class="service-timeframe">Timeframe: <?php echo esc_html($service['timeframe']); ?></p>
                <a href="/services/<?php echo esc_attr($service['id']); ?>" class="btn-primary">Learn More</a>
            </div>
        <?php endforeach; ?>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('mc_services_grid', 'mc_services_grid_shortcode');

/**
 * Contact info shortcode
 */
function mc_contact_info_shortcode($atts) {
    $contact = mc_get_contact_info();

    ob_start();
    ?>
    <div class="contact-info">
        <div class="contact-item">
            <div class="contact-icon">📧</div>
            <h4>Email</h4>
            <p><?php echo esc_html($contact['email']); ?></p>
        </div>
        <div class="contact-item">
            <div class="contact-icon">📞</div>
            <h4>Phone</h4>
            <p><?php echo esc_html($contact['phone']); ?></p>
        </div>
        <div class="contact-item">
            <div class="contact-icon">🕒</div>
            <h4>Hours</h4>
            <p><?php echo esc_html($contact['hours']); ?></p>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('mc_contact_info', 'mc_contact_info_shortcode');

/**
 * Service details shortcode
 */
function mc_service_details_shortcode($atts) {
    $atts = shortcode_atts(array(
        'service_id' => ''
    ), $atts);

    if (empty($atts['service_id'])) {
        return '<p>Service ID required.</p>';
    }

    $service = mc_get_service($atts['service_id']);

    if (!$service) {
        return '<p>Service not found.</p>';
    }

    ob_start();
    ?>
    <div class="service-details">
        <div class="service-header">
            <div class="service-icon"><?php echo esc_html($service['icon']); ?></div>
            <h2><?php echo esc_html($service['name']); ?></h2>
            <p><?php echo esc_html($service['description']); ?></p>
        </div>

        <div class="service-info">
            <div class="service-tasks">
                <h3>What We Do</h3>
                <ul>
                    <?php foreach ($service['tasks'] as $task): ?>
                        <li><?php echo esc_html($task); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <div class="service-benefits">
                <h3>Benefits</h3>
                <ul>
                    <?php foreach ($service['benefits'] as $benefit): ?>
                        <li><?php echo esc_html($benefit); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <div class="service-timeframe">
                <h3>Timeframe</h3>
                <p><?php echo esc_html($service['timeframe']); ?></p>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('mc_service_details', 'mc_service_details_shortcode');

/**
 * Remove WordPress core "Skip to content" link
 */
function mc_remove_skip_link() {
    remove_action('wp_footer', 'the_block_template_skip_link');
    remove_action('wp_enqueue_scripts', 'wp_enqueue_block_template_skip_link');
}
add_action('init', 'mc_remove_skip_link');

/**
 * Hide skip link with CSS as additional fallback
 */
function mc_hide_skip_link_css() {
    echo '<style>
        .skip-link,
        .screen-reader-shortcut,
        a[href="#main"],
        a[href="#wp--skip-link--target"],
        #wp-skip-link {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            position: absolute !important;
            left: -9999px !important;
        }
    </style>';
}
add_action('wp_head', 'mc_hide_skip_link_css');

/**
 * Custom admin functions
 */

/**
 * Add admin menu for JSON data management
 */
function mc_admin_menu() {
    add_theme_page(
        'MallorcaHomeServices Settings',
        'MHS Settings',
        'manage_options',
        'mc-settings',
        'mc_admin_page'
    );
}
add_action('admin_menu', 'mc_admin_menu');

/**
 * Admin page content
 */
function mc_admin_page() {
    $json_data = mc_get_json_data();
    ?>
    <div class="wrap">
        <h1>MallorcaHomeServices Settings</h1>

        <div class="card">
            <h2>JSON Data Status</h2>
            <?php if ($json_data): ?>
                <p style="color: green;">✓ JSON data loaded successfully</p>
                <p><strong>Services found:</strong> <?php echo count(mc_get_services()); ?></p>
                <p><strong>Last updated:</strong> <?php echo date('Y-m-d H:i:s', filemtime('H:/VSC/MallorcaHomeServices/mallorcahomeservices-augment.json')); ?></p>
            <?php else: ?>
                <p style="color: red;">✗ JSON data not found or invalid</p>
                <p>Please ensure the JSON file exists at: <code>H:/VSC/MallorcaHomeServices/mallorcahomeservices-augment.json</code></p>
            <?php endif; ?>
        </div>

        <div class="card">
            <h2>Available Shortcodes</h2>
            <ul>
                <li><code>[mc_hero]</code> - Display hero section</li>
                <li><code>[mc_services_grid limit="6" columns="3"]</code> - Display services grid</li>
                <li><code>[mc_contact_info]</code> - Display contact information</li>
                <li><code>[mc_service_details service_id="general-renovation"]</code> - Display specific service details</li>
            </ul>
        </div>

        <div class="card">
            <h2>Theme Instructions</h2>
            <ol>
                <li>Create pages: Home, About, Services, Contact</li>
                <li>Create individual service pages for each service</li>
                <li>Set up navigation menus in Appearance > Menus</li>
                <li>Install contact form plugin (Contact Form 7, WPForms, etc.)</li>
                <li>Replace contact form placeholder in Contact page template</li>
            </ol>
        </div>
    </div>
    <?php
}
