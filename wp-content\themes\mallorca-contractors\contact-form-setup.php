<?php
/**
 * Contact Form 7 Setup for MallorcaHomeServices
 * This file sets up the contact form configuration
 */

// Contact Form 7 Form Template
$contact_form_template = '
<div class="form-row">
    <div class="form-group">
        [text* your-name placeholder "Your Name *"]
    </div>
</div>

<div class="form-row">
    <div class="form-group">
        [email* your-email placeholder "Your Email *"]
    </div>
</div>

<div class="form-row">
    <div class="form-group">
        [text your-subject placeholder "Subject"]
    </div>
</div>

<div class="form-row">
    <div class="form-group">
        [textarea* your-message placeholder "Your Message *"]
    </div>
</div>

<div class="form-row">
    <div class="form-group">
        [submit "Send Message"]
    </div>
</div>
';

// Email Template for Contact Form 7
$email_template = '
Subject: New Contact Form Submission from [your-name]

From: [your-name] <[your-email]>
Subject: [your-subject]

Message Body:
[your-message]

--
This email was sent from the contact form on MallorcaHomeServices website.
';

// Auto-reply Email Template
$auto_reply_template = '
Subject: Thank you for contacting MallorcaHomeServices

Dear [your-name],

Thank you for contacting MallorcaHomeServices. We have received your message and will get back to you within 24 hours.

Your message:
Subject: [your-subject]
Message: [your-message]

Best regards,
MallorcaHomeServices Team
<EMAIL>

--
This is an automated response. Please do not reply to this email.
';

// Contact Form 7 Configuration
$cf7_config = array(
    'title' => 'MallorcaHomeServices Contact Form',
    'form' => $contact_form_template,
    'mail' => array(
        'subject' => 'New Contact Form Submission from [your-name]',
        'sender' => '[your-name] <[your-email]>',
        'body' => $email_template,
        'recipient' => '<EMAIL>',
        'additional_headers' => 'Reply-To: [your-email]',
    ),
    'mail_2' => array(
        'active' => true,
        'subject' => 'Thank you for contacting MallorcaHomeServices',
        'sender' => 'MallorcaHomeServices <<EMAIL>>',
        'body' => $auto_reply_template,
        'recipient' => '[your-email]',
    ),
    'messages' => array(
        'mail_sent_ok' => 'Thank you for your message. We will get back to you soon!',
        'mail_sent_ng' => 'There was an error sending your message. Please try again.',
        'validation_error' => 'One or more fields have an error. Please check and try again.',
        'spam' => 'There was an error sending your message. Please try again.',
        'accept_terms' => 'You must accept the terms and conditions before sending your message.',
        'invalid_required' => 'Please fill out this field.',
        'invalid_too_long' => 'This field is too long.',
        'invalid_too_short' => 'This field is too short.',
    )
);

// Instructions for manual setup
echo "<!-- 
CONTACT FORM 7 SETUP INSTRUCTIONS:

1. Go to WordPress Admin > Contact > Contact Forms
2. Click 'Add New'
3. Title: MallorcaHomeServices Contact Form
4. Copy the form template above into the Form tab
5. Configure Mail settings:
   - To: <EMAIL>
   - From: [your-name] <[your-email]>
   - Subject: New Contact Form Submission from [your-name]
   - Message Body: Copy the email template above
6. Enable Mail (2) for auto-reply:
   - To: [your-email]
   - From: MallorcaHomeServices <<EMAIL>>
   - Subject: Thank you for contacting MallorcaHomeServices
   - Message Body: Copy the auto-reply template above
7. Save the form and copy the shortcode
8. Replace the HTML form in page-contact.html with the shortcode

Example shortcode: [contact-form-7 id="123" title="MallorcaHomeServices Contact Form"]
-->";

?>
