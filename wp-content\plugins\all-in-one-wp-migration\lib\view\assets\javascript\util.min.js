(()=>{var r={939:r=>{var t=jQuery;r.exports={random:function(r,t){for(var n="",e="abcdefghijklmnopqrstuvwxyz0123456789",i=0;i<r;i++)n+=e.charAt(Math.floor(36*Math.random()));return t?n+t:n},form:function(r){return t(r).serializeArray()},ucfirst:function(r){return r.charAt(0).toUpperCase()+r.slice(1)},list:function(r){if(t.isPlainObject(r)){var n=[],e=decodeURIComponent(t.param(r)).split("&");return t.each(e,(function(r,t){var e=t.split("=");n.push({name:e[0],value:e[1]})})),n}return r},findValueByName:function(r,t){if(t in r)return r[t];var n=r.find((function(r){return r.name===t}));return n?n.value:void 0},json:function(r){if("string"==typeof r){var t=r.match(/{[\s\S]+}/);if(null!==t)return t[0]}return!1},sizeFormat:function(r){if(0===parseInt(r))return"0 B";var t=Math.floor(Math.log(r)/Math.log(1024)),n=1*(r/Math.pow(1024,t)).toFixed(2);return isNaN(n)?"0 B":n+" "+["B","KB","MB","GB","TB","PB","EB","ZB","YB"][t]},dirname:function(r){return r.replace(/\\/g,"/").replace(/\/[^/]*\/?$/,"")},basename:function(r){return r.replace(/\\/g,"/").replace(/.*\//,"")}}}},t={};function n(e){var i=t[e];if(void 0!==i)return i.exports;var a=t[e]={exports:{}};return r[e](a,a.exports,n),a.exports}n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(r){if("object"==typeof window)return window}}();var e=n(939);n.g.Ai1wm=jQuery.extend({},n.g.Ai1wm,{Util:e})})();