/**
 * Mallorca Contractors Theme Scripts
 * 
 * @package Mallorca_Contractors
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        initStickyHeader();
        initMegaMenu();
        initServiceCards();
        initSmoothScrolling();
        initMobileMenu();
        initContactForm();
    });

    /**
     * Initialize sticky header
     */
    function initStickyHeader() {
        var header = $('.site-header');
        var headerOffset = header.offset().top;

        $(window).scroll(function() {
            if ($(window).scrollTop() > headerOffset) {
                header.addClass('sticky');
            } else {
                header.removeClass('sticky');
            }
        });
    }

    /**
     * Initialize mega menu functionality
     */
    function initMegaMenu() {
        $('.services-menu').hover(
            function() {
                $(this).find('.mega-menu').fadeIn(200);
            },
            function() {
                $(this).find('.mega-menu').fadeOut(200);
            }
        );
    }

    /**
     * Initialize service card animations
     */
    function initServiceCards() {
        $('.service-card').hover(
            function() {
                $(this).addClass('hovered');
            },
            function() {
                $(this).removeClass('hovered');
            }
        );

        // Add intersection observer for scroll animations
        if ('IntersectionObserver' in window) {
            var observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, {
                threshold: 0.1
            });

            $('.service-card, .vertical-block').each(function() {
                observer.observe(this);
            });
        }
    }

    /**
     * Initialize smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        $('a[href^="#"]').on('click', function(e) {
            var target = $(this.getAttribute('href'));
            
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 800);
            }
        });
    }

    /**
     * Initialize mobile menu functionality
     */
    function initMobileMenu() {
        $('.mobile-menu-toggle').on('click', function() {
            $(this).toggleClass('active');
            $('.main-navigation').toggleClass('active');
            $('body').toggleClass('menu-open');
        });

        // Close menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.main-navigation, .mobile-menu-toggle').length) {
                $('.mobile-menu-toggle').removeClass('active');
                $('.main-navigation').removeClass('active');
                $('body').removeClass('menu-open');
            }
        });
    }

    /**
     * Initialize contact form enhancements
     */
    function initContactForm() {
        // Add loading states to contact forms
        $('.contact-form').on('submit', function() {
            var $form = $(this);
            var $submitBtn = $form.find('input[type="submit"], button[type="submit"]');
            
            $submitBtn.prop('disabled', true).addClass('loading');
            
            // Re-enable after 3 seconds (adjust based on your form handling)
            setTimeout(function() {
                $submitBtn.prop('disabled', false).removeClass('loading');
            }, 3000);
        });

        // Form validation enhancements
        $('.contact-form input, .contact-form textarea').on('blur', function() {
            var $field = $(this);
            var value = $field.val().trim();
            
            if ($field.prop('required') && !value) {
                $field.addClass('error');
            } else {
                $field.removeClass('error');
            }
            
            // Email validation
            if ($field.attr('type') === 'email' && value) {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    $field.addClass('error');
                } else {
                    $field.removeClass('error');
                }
            }
        });
    }

    /**
     * Initialize scroll-to-top functionality
     */
    function initScrollToTop() {
        // Add scroll to top button
        $('body').append('<button id="scroll-to-top" class="scroll-to-top" aria-label="Scroll to top">↑</button>');
        
        var $scrollBtn = $('#scroll-to-top');
        
        $(window).scroll(function() {
            if ($(window).scrollTop() > 300) {
                $scrollBtn.addClass('visible');
            } else {
                $scrollBtn.removeClass('visible');
            }
        });
        
        $scrollBtn.on('click', function() {
            $('html, body').animate({
                scrollTop: 0
            }, 600);
        });
    }

    /**
     * Initialize lazy loading for images
     */
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            var imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        var img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            $('.lazy').each(function() {
                imageObserver.observe(this);
            });
        }
    }

    /**
     * Initialize service page functionality
     */
    function initServicePage() {
        // Service page specific animations
        $('.service-process-step').each(function(index) {
            $(this).css('animation-delay', (index * 0.2) + 's');
        });

        // Service benefits accordion
        $('.service-benefits-toggle').on('click', function() {
            var $content = $(this).next('.service-benefits-content');
            $(this).toggleClass('active');
            $content.slideToggle(300);
        });
    }

    /**
     * Initialize performance optimizations
     */
    function initPerformanceOptimizations() {
        // Debounce scroll events
        var scrollTimer = null;
        $(window).on('scroll', function() {
            if (scrollTimer !== null) {
                clearTimeout(scrollTimer);
            }
            scrollTimer = setTimeout(function() {
                // Scroll-dependent functions here
            }, 150);
        });

        // Preload critical pages
        $('a[href^="/services"], a[href^="/contact"]').on('mouseenter', function() {
            var link = this.href;
            if (!$('link[rel="prefetch"][href="' + link + '"]').length) {
                $('<link rel="prefetch" href="' + link + '">').appendTo('head');
            }
        });
    }

    /**
     * Initialize accessibility enhancements
     */
    function initAccessibility() {
        // Focus management for mobile menu
        $('.mobile-menu-toggle').on('click', function() {
            if ($('.main-navigation').hasClass('active')) {
                $('.main-navigation a:first').focus();
            }
        });

        // Keyboard navigation for service cards
        $('.service-card').attr('tabindex', '0').on('keydown', function(e) {
            if (e.which === 13 || e.which === 32) { // Enter or Space
                e.preventDefault();
                $(this).find('a').first()[0].click();
            }
        });
    }

    // Initialize additional features
    $(window).on('load', function() {
        initScrollToTop();
        initLazyLoading();
        initServicePage();
        initPerformanceOptimizations();
        initAccessibility();
    });

    // Handle window resize
    $(window).on('resize', function() {
        // Responsive adjustments
        if ($(window).width() > 768) {
            $('.main-navigation').removeClass('active');
            $('.mobile-menu-toggle').removeClass('active');
            $('body').removeClass('menu-open');
        }
    });

})(jQuery);
