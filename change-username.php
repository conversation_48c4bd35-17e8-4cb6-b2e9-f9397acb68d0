<?php
/**
 * WordPress Username Change Script
 * Changes username from "test" to "mallorcahomeadmin"
 * 
 * IMPORTANT: Run this script once and then delete it for security
 */

// Load WordPress configuration
require_once('wp-config.php');

// Database connection settings from wp-config.php
$db_host = DB_HOST;
$db_name = DB_NAME;
$db_user = DB_USER;
$db_password = DB_PASSWORD;
$table_prefix = $table_prefix;

// Old and new usernames
$old_username = 'test';
$new_username = 'mallorcahomeadmin';

try {
    // Create database connection
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>WordPress Username Change Script</h2>\n";
    echo "<p>Changing username from '<strong>$old_username</strong>' to '<strong>$new_username</strong>'</p>\n";
    
    // Check if old username exists
    $check_stmt = $pdo->prepare("SELECT ID, user_login, user_nicename, display_name FROM {$table_prefix}users WHERE user_login = ?");
    $check_stmt->execute([$old_username]);
    $user = $check_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "<p style='color: red;'>❌ User '$old_username' not found in database.</p>\n";
        exit;
    }
    
    echo "<p>✅ Found user: ID {$user['ID']}, Login: {$user['user_login']}, Display Name: {$user['display_name']}</p>\n";
    
    // Check if new username already exists
    $check_new_stmt = $pdo->prepare("SELECT ID FROM {$table_prefix}users WHERE user_login = ?");
    $check_new_stmt->execute([$new_username]);
    $existing_user = $check_new_stmt->fetch();
    
    if ($existing_user) {
        echo "<p style='color: red;'>❌ Username '$new_username' already exists. Cannot proceed.</p>\n";
        exit;
    }
    
    // Start transaction
    $pdo->beginTransaction();
    
    try {
        // Update user_login and user_nicename
        $update_stmt = $pdo->prepare("
            UPDATE {$table_prefix}users 
            SET user_login = ?, user_nicename = ? 
            WHERE user_login = ?
        ");
        $update_stmt->execute([$new_username, $new_username, $old_username]);
        
        echo "<p>✅ Updated user_login and user_nicename to '$new_username'</p>\n";
        
        // Update display_name if it was the same as the old username
        if ($user['display_name'] === $old_username) {
            $display_stmt = $pdo->prepare("
                UPDATE {$table_prefix}users 
                SET display_name = ? 
                WHERE user_login = ?
            ");
            $display_stmt->execute([$new_username, $new_username]);
            echo "<p>✅ Updated display_name to '$new_username'</p>\n";
        }
        
        // Update user meta if needed (author slug)
        $meta_stmt = $pdo->prepare("
            UPDATE {$table_prefix}usermeta 
            SET meta_value = ? 
            WHERE user_id = ? AND meta_key = 'nickname'
        ");
        $meta_stmt->execute([$new_username, $user['ID']]);
        
        // Commit transaction
        $pdo->commit();
        
        echo "<h3 style='color: green;'>🎉 SUCCESS!</h3>\n";
        echo "<p>Username successfully changed from '<strong>$old_username</strong>' to '<strong>$new_username</strong>'</p>\n";
        echo "<p><strong>Next steps:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>✅ You can now log in with username: <strong>$new_username</strong></li>\n";
        echo "<li>✅ Use the same password as before</li>\n";
        echo "<li>🔒 <strong>IMPORTANT:</strong> Delete this script file for security</li>\n";
        echo "</ul>\n";
        
        // Show updated user info
        $verify_stmt = $pdo->prepare("SELECT ID, user_login, user_nicename, display_name FROM {$table_prefix}users WHERE user_login = ?");
        $verify_stmt->execute([$new_username]);
        $updated_user = $verify_stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h4>Updated User Information:</h4>\n";
        echo "<ul>\n";
        echo "<li>User ID: {$updated_user['ID']}</li>\n";
        echo "<li>Username: {$updated_user['user_login']}</li>\n";
        echo "<li>Nice Name: {$updated_user['user_nicename']}</li>\n";
        echo "<li>Display Name: {$updated_user['display_name']}</li>\n";
        echo "</ul>\n";
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $pdo->rollback();
        throw $e;
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
}

echo "<hr>\n";
echo "<p><em>Script completed. Remember to delete this file after successful username change.</em></p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background-color: #f5f5f5;
}
h2 {
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}
p {
    line-height: 1.6;
}
ul {
    background-color: white;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #0073aa;
}
</style>
