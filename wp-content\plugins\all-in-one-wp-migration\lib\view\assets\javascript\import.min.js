(()=>{var e={665:(e,t,a)=>{var o=a(575),i=jQuery,r=function(){var e=this;this.params=[],this.modal=new o,this.modal.onConfirm=function(t){e.onConfirm(t)},this.modal.onBlogs=function(t){e.onBlogs(t)},this.modal.onStop=function(t){t=(t||[]).concat({name:"ai1wm_import_cancel",value:1}),e.onStop(t)},this.modal.onDiskSpaceConfirm=function(t){e.onDiskSpaceConfirm(t)},this.modal.onDecryptPassword=function(t,a){e.onDecryptPassword(t,a)}};r.prototype.setParams=function(e){this.params=Ai1wm.Util.list(e)},r.prototype.start=function(e,t){var a=this;if(0===(t=t||0)&&this.stopImport(!1),!this.isImportStopped()){i(window).bind("beforeunload",(function(){return ai1wm_locale.stop_importing_your_website})),this.setStatus({type:"info",message:ai1wm_locale.preparing_to_import});var o=this.params.concat({name:"secret_key",value:ai1wm_import.secret_key});e&&(o=o.concat(Ai1wm.Util.list(e))),i.ajax({url:ai1wm_import.ajax.url,type:"POST",dataType:"json",data:o,dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(){a.getStatus()})).done((function(e){e&&a.run(e)})).fail((function(i){var r=1e3*t;try{var s=Ai1wm.Util.json(i.responseText);if(s){var n=JSON.parse(s).errors.pop();if(n.message)return a.stopImport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_import,message:n.message,nonce:Ai1wm.Util.findValueByName(o,"storage")})}}catch(e){}if(t>=5)return a.stopImport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_import,message:ai1wm_locale.unable_to_start_the_import,nonce:Ai1wm.Util.findValueByName(o,"storage")});t++,setTimeout(a.start.bind(a,e,t),r)}))}},r.prototype.run=function(e,t){var a=this;t=t||0,this.isImportStopped()||i.ajax({url:ai1wm_import.ajax.url,type:"POST",dataType:"json",data:e,dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(e){e&&a.run(e)})).fail((function(o){var i=1e3*t;try{var r=Ai1wm.Util.json(o.responseText);if(r){var s=JSON.parse(r).errors.pop();if(s.message)return a.stopImport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_import,message:s.message,nonce:Ai1wm.Util.findValueByName(e,"storage")})}}catch(e){}t++,setTimeout(a.run.bind(a,e,t),i)}))},r.prototype.decryptPassword=function(e,t,a){var o=this;if(a=a||0,!this.isImportStopped()){this.params=this.params.concat({name:"decryption_password",value:t});var r=this.params.concat({name:"secret_key",value:ai1wm_import.secret_key}).concat({name:"priority",value:90});i.ajax({url:ai1wm_import.ajax.url,type:"POST",dataType:"json",data:r,dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(){o.getStatus()})).done((function(e){e&&o.run(e)})).fail((function(i){var s=1e3*a;try{var n=Ai1wm.Util.json(i.responseText);if(n){var p=JSON.parse(n).errors.pop();if(p.message)return o.stopImport(!0),void o.setStatus({type:"error",title:ai1wm_locale.unable_to_import,message:p.message,nonce:Ai1wm.Util.findValueByName(r,"storage")})}}catch(e){}if(a>=5)return o.stopImport(!0),void o.setStatus({type:"error",title:ai1wm_locale.unable_to_import,message:ai1wm_locale.unable_to_check_decryption_password,nonce:Ai1wm.Util.findValueByName(r,"storage")});a++,setTimeout(o.decryptPassword.bind(o,e,t,a),s)}))}},r.prototype.confirm=function(e,t){var a=this;if(t=t||0,!this.isImportStopped()){var o=this.params.concat({name:"secret_key",value:ai1wm_import.secret_key}).concat({name:"priority",value:150});e&&(o=o.concat(Ai1wm.Util.list(e))),i.ajax({url:ai1wm_import.ajax.url,type:"POST",dataType:"json",data:o,dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(){a.getStatus()})).done((function(e){e&&a.run(e)})).fail((function(i){var r=1e3*t;try{var s=Ai1wm.Util.json(i.responseText);if(s){var n=JSON.parse(s).errors.pop();if(n.message)return a.stopImport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_import,message:n.message,nonce:Ai1wm.Util.findValueByName(o,"storage")})}}catch(e){}if(t>=5)return a.stopImport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_import,message:ai1wm_locale.unable_to_confirm_the_import,nonce:Ai1wm.Util.findValueByName(o,"storage")});t++,setTimeout(a.confirm.bind(a,e,t),r)}))}},r.prototype.checkDiskSpace=function(e,t){this.diskSpaceCallback=t;var a=parseInt(ai1wm_disk_space.free,10),o=parseInt(ai1wm_disk_space.factor,10),i=parseInt(ai1wm_disk_space.extra,10);if(a>=0){var r=e*o+i;if(r>a)return void this.setStatus({type:"disk_space_confirm",message:ai1wm_locale.out_of_disk_space.replace("%s",Ai1wm.Util.sizeFormat(r-a))})}t()},r.prototype.blogs=function(e,t){var a=this;if(t=t||0,!this.isImportStopped()){var o=this.params.concat({name:"secret_key",value:ai1wm_import.secret_key}).concat({name:"priority",value:150});e&&(o=o.concat(Ai1wm.Util.list(e))),i.ajax({url:ai1wm_import.ajax.url,type:"POST",dataType:"json",data:o,dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(){a.getStatus()})).done((function(e){e&&a.run(e)})).fail((function(i){var r=1e3*t;try{var s=Ai1wm.Util.json(i.responseText);if(s){var n=JSON.parse(s).errors.pop();if(n.message)return a.stopImport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_import,message:n.message,nonce:Ai1wm.Util.findValueByName(o,"storage")})}}catch(e){}if(t>=5)return a.stopImport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_import,message:ai1wm_locale.unable_to_prepare_blogs_on_import,nonce:Ai1wm.Util.findValueByName(o,"storage")});t++,setTimeout(a.blogs.bind(a,e,t),r)}))}},r.prototype.clean=function(e,t){var a=this;0===(t=t||0)&&this.stopImport(!0),this.setStatus({type:"info",message:ai1wm_locale.please_wait_stopping_the_import});var o=this.params.concat({name:"secret_key",value:ai1wm_import.secret_key}).concat({name:"priority",value:400});e&&(o=o.concat(Ai1wm.Util.list(e))),i.ajax({url:ai1wm_import.ajax.url,type:"POST",dataType:"json",data:o,dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(){i(window).unbind("beforeunload"),a.modal.destroy()})).fail((function(i){var r=1e3*t;try{var s=Ai1wm.Util.json(i.responseText);if(s){var n=JSON.parse(s).errors.pop();if(n.message)return a.stopImport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_import,message:n.message,nonce:Ai1wm.Util.findValueByName(o,"storage")})}}catch(e){}if(t>=5)return a.stopImport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_import,message:ai1wm_locale.unable_to_stop_the_import,nonce:Ai1wm.Util.findValueByName(o,"storage")});t++,setTimeout(a.clean.bind(a,e,t),r)}))},r.prototype.getStatus=function(){var e=this;this.isImportStopped()||(this.statusXhr=i.ajax({url:ai1wm_import.status.url,type:"GET",dataType:"json",cache:!1,dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(t){if(t)switch(e.setStatus(t),t.type){case"done":case"error":return void i(window).unbind("beforeunload");case"confirm":case"disk_space_confirm":case"blogs":case"backup_is_encrypted":return}setTimeout(e.getStatus.bind(e),3e3)})).fail((function(){setTimeout(e.getStatus.bind(e),3e3)})))},r.prototype.setStatus=function(e){this.modal.render(e)},r.prototype.onConfirm=function(e){this.confirm(e)},r.prototype.onDecryptPassword=function(e,t){this.decryptPassword(t,e)},r.prototype.onBlogs=function(e){this.blogs(e)},r.prototype.onStop=function(e){this.clean(e)},r.prototype.onDiskSpaceConfirm=function(e){this.diskSpaceCallback(e)},r.prototype.stopImport=function(e){try{e&&this.statusXhr&&this.statusXhr.abort()}finally{this.isStopped=e}},r.prototype.isImportStopped=function(){return this.isStopped},e.exports=r},575:e=>{var t=jQuery,a=function(){var e=this;this.error=function(a){var o=t("<div></div>"),i=t("<section></section>"),r=t("<h1></h1>"),s=t("<p></p>").html(a.message).addClass(a.leftAligned?"ai1wm-left-aligned":""),n=t("<div></div>"),p=t("<span></span>").addClass("ai1wm-title-red").text(a.title),l=t('<button type="button" class="ai1wm-button-red"></button>').on("click",(function(){e.destroy()}));if(l.append(ai1wm_locale.close_import),n.append(l),r.append(p),i.append(r).append(s),a.nonce){var c=t('<a target="_blank"></a>');c.text(ai1wm_locale.view_error_log_button),c.prop("href",ai1wm_export.storage.url+"/"+ai1wm_export.error_log.pattern.replace("%s",a.nonce)),i.append(t("<div></div>").append(c))}o.append(i).append(n),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.progress=function(a){if(this.progress.progressBarMeter&&this.progress.progressBarMeter.width(a.percent+"%"),this.progress.progressBarPercent)this.progress.progressBarPercent.text(a.percent+"%");else{var o=t("<div></div>"),i=t("<section></section>"),r=t("<h1></h1>"),s=t("<div></div>"),n=t('<span class="ai1wm-progress-bar"></span>');this.progress.progressBarMeter=t('<span class="ai1wm-progress-bar-meter"></span>').width(a.percent+"%"),this.progress.progressBarPercent=t('<span class="ai1wm-progress-bar-percent"></span>').text(a.percent+"%");var p=t('<button type="button" class="ai1wm-button-red"></button>').on("click",(function(){p.attr("disabled","disabled"),e.onStop()}));p.append('<i class="ai1wm-icon-notification"></i> '+ai1wm_locale.stop_import),n.append(this.progress.progressBarMeter).append(this.progress.progressBarPercent),s.append(p),r.append(n),i.append(r),o.append(i).append(s),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()}},this.pro=function(a){var o=t("<div></div>"),i=t("<section></section>"),r=t("<h1></h1>"),s=t('<p class="ai1wm-import-modal-content"></p>').html(a.message),n=t("<div></div>"),p=t('<i class="ai1wm-icon-notification"></i>'),l=t('<button type="button" class="ai1wm-button-gray"></button>').on("click",(function(){e.destroy()}));l.append(ai1wm_locale.close_import),n.append(l),r.append(p),i.append(r).append(s),o.append(i).append(n),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.confirm=function(a){var o=t("<div></div>"),i=t("<section></section>"),r=t("<h1></h1>"),s=t('<p class="ai1wm-import-modal-content"></p>').html(a.message),n=t('<div class="ai1wm-import-modal-actions"></div>'),p=t('<i class="ai1wm-icon-notification"></i>'),l=t('<button type="button" class="ai1wm-button-gray"></button>').on("click",(function(){l.attr("disabled","disabled"),e.onStop()})),c=t('<button type="button" class="ai1wm-button-green"></button>').on("click",(function(){c.attr("disabled","disabled"),e.onConfirm()}));l.append(ai1wm_locale.close_import),c.append(ai1wm_locale.confirm_import+" &gt;"),n.append(l),n.append(c),r.append(p),i.append(r).append(s),o.append(i).append(n),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.diskSpaceConfirm=function(a){var o=t("<div></div>"),i=t("<section></section>"),r=t("<h1></h1>"),s=t('<p class="ai1wm-import-modal-content"></p>').html(a.message),n=t('<div class="ai1wm-import-modal-actions"></div>'),p=t('<i class="ai1wm-icon-notification"></i>'),l=t('<button type="button" class="ai1wm-button-gray"></button>').on("click",(function(){e.destroy()})),c=t('<button type="button" class="ai1wm-button-green"></button>').on("click",(function(){t(this).attr("disabled","disabled"),e.onDiskSpaceConfirm()}));l.append(ai1wm_locale.close_import),c.append(ai1wm_locale.confirm_disk_space),n.append(l),n.append(c),r.append(p),i.append(r).append(s),o.append(i).append(n),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.blogs=function(a){var o=t("<form></form>").on("submit",(function(t){t.preventDefault(),l.attr("disabled","disabled"),e.onBlogs(o.serializeArray())})),i=t("<section></section>"),r=t("<h1></h1>"),s=t("<p></p>").html(a.message),n=t("<div></div>"),p=t("<span></span>").addClass("ai1wm-title-grey").text(a.title),l=t('<button type="submit" class="ai1wm-button-green"></button>');l.append(ai1wm_locale.continue_import),n.append(l),r.append(p),i.append(r).append(s),o.append(i).append(n),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.info=function(a){var o=t("<div></div>"),i=t("<section></section>"),r=t("<h1></h1>"),s=t("<p></p>").html(a.message),n=t("<div></div>"),p=t('<span class="ai1wm-loader"></span>'),l=t("<p></p>").html(ai1wm_locale.please_do_not_close_this_browser),c=t('<div class="ai1wm-import-modal-notice"></div>');c.append(l),n.append(c),r.append(p),i.append(r).append(s),o.append(i).append(n),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.done=function(a){var o=t("<div></div>"),i=t("<section></section>"),r=t("<h1></h1>"),s=t('<p class="ai1wm-import-modal-content-done"></p>').html(a.message),n=t('<div class="ai1wm-import-modal-actions"></div>'),p=t("<span></span>").addClass("ai1wm-title-green").text(a.title),l=t('<button type="button" class="ai1wm-button-green"></button>').on("click",(function(){e.destroy()}));l.append(ai1wm_locale.finish_import+" &gt;"),n.append(l),r.append(p),i.append(r).append(s),o.append(i).append(n),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.backup_is_encrypted=function(a){var o=t("<div></div>"),i=t('<section class="ai1wm-decrypt-backup-section"></section>'),r=t("<h1></h1>").html(ai1wm_locale.backup_encrypted),s=t('<p class="ai1wm-import-decrypt-password-modal-content"></p>').html(ai1wm_locale.backup_encrypted_message),n=t('<button type="button" class="ai1wm-button-green"></button>').on("click",(function(){var a=t("#ai1wm-backup-decrypt-password"),o=t("#ai1wm-backup-decrypt-password-confirmation");a.val().length&&a.val()===o.val()?(n.attr("disabled","disabled"),e.onDecryptPassword(a.val())):(o.parent().addClass("ai1wm-has-error"),a.parent().addClass("ai1wm-has-error"))})),p=t('<button type="button" class="ai1wm-button-gray"></button>').on("click",(function(){p.attr("disabled","disabled"),e.onStop()})),l=t('<form class="ai1wm-decrypt-form"></form>'),c=t('<div class="ai1wm-input-password-container"></div>'),d=t('<input type="password" name="password" id="ai1wm-backup-decrypt-password" required />').prop("placeholder",ai1wm_locale.enter_password).on("keyup",(function(){var e=t(this),a=t("#ai1wm-backup-decrypt-password-confirmation");e.val()!==a.val()?(a.parent().addClass("ai1wm-has-error"),e.parent().addClass("ai1wm-has-error")):(e.parent().removeClass("ai1wm-has-error"),a.parent().removeClass("ai1wm-has-error"))})),m=t('<a href="#ai1wm-backup-decrypt-password" class="ai1wm-toggle-password-visibility ai1wm-icon-eye-blocked"></a>').on("click",(function(){return t(this).toggleClass("ai1wm-icon-eye ai1wm-icon-eye-blocked"),t(this).prev().prop("type",(function(e,t){return"text"===t?"password":"text"})),!1}));if(c.append(d).append(m),a.error){c.addClass("ai1wm-has-error");var u=t('<div class="ai1wm-error-message"></div>').html(a.error);c.append(u)}var w=t('<div class="ai1wm-input-password-container"></div>'),f=t('<input type="password" name="password_confirmation" id="ai1wm-backup-decrypt-password-confirmation" required />').prop("placeholder",ai1wm_locale.repeat_password).on("keyup",(function(){var e=t(this),a=t("#ai1wm-backup-decrypt-password");d.val()!==e.val()?(a.parent().addClass("ai1wm-has-error"),e.parent().addClass("ai1wm-has-error")):(a.parent().removeClass("ai1wm-has-error"),e.parent().removeClass("ai1wm-has-error"))})),h=t('<a href="#ai1wm-backup-decrypt-password-confirmation" class="ai1wm-toggle-password-visibility ai1wm-icon-eye-blocked"></a>').on("click",(function(){return t(this).toggleClass("ai1wm-icon-eye ai1wm-icon-eye-blocked"),t(this).prev().prop("type",(function(e,t){return"text"===t?"password":"text"})),!1})),_=t('<div class="ai1wm-error-message"></div>').html(ai1wm_locale.passwords_do_not_match);w.append(f).append(h).append(_),n.append(ai1wm_locale.submit),p.append(ai1wm_locale.close_import);var v=t('<div class="ai1wm-backup-decrypt-button-container"></div>');v.append(p).append(n),l.append(c).append(w),i.append(r).append(s).append(l).append(v),o.append(i),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.server_cannot_decrypt=function(a){var o=t("<div></div>"),i=t("<section></section>"),r=t("<h1></h1>"),s=t("<p></p>").html(a.message),n=t('<i class="ai1wm-icon-notification"></i>'),p=t("<div></div>"),l=t('<button type="button" class="ai1wm-button-red"></button>').on("click",(function(){l.attr("disabled","disabled"),e.onStop()}));l.append(ai1wm_locale.close_import),p.append(l),r.append(n),i.append(r).append(s),o.append(i).append(p),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.overlay=t('<div class="ai1wm-overlay"></div>'),this.modal=t('<div class="ai1wm-modal-container" role="dialog" tabindex="-1"></div>'),t("body").append(this.overlay).append(this.modal)};a.prototype.render=function(e){switch(t(document).trigger("ai1wm-import-status",e),e.type){case"pro":this.pro(e);break;case"error":this.error(e);break;case"confirm":this.confirm(e);break;case"disk_space_confirm":this.diskSpaceConfirm(e);break;case"blogs":this.blogs(e);break;case"progress":this.progress(e);break;case"info":this.info(e);break;case"done":this.done(e);break;case"backup_is_encrypted":this.backup_is_encrypted(e);break;case"server_cannot_decrypt":this.server_cannot_decrypt(e)}},a.prototype.destroy=function(){this.modal.hide(),this.overlay.hide(),this.progress.progressBarMeter=null,this.progress.progressBarPercent=null},e.exports=a},174:(e,t,a)=>{var o=a(665),i=jQuery,r=function(){};r.prototype.setDefaultValues=function(){this.model=new o,this.stopUpload=!1},r.prototype.init=function(){var e=this,t=i("#ai1wm-import-form"),a=i("#ai1wm-import-file"),o=i("#ai1wm-drag-drop-area");a.on("change",(function(a){e.setDefaultValues();var o=a.target.files.item(0);o&&(e.fileSize=o.size,e.fileSize>ai1wm_uploader.max_file_size?e.model.setStatus({type:"pro",message:ai1wm_locale.import_from_file}):e.model.checkDiskSpace(e.fileSize,(function(){try{e.onFilesAdded(o),e.onBeforeUpload(o),e.upload(o)}catch(t){e.onError(t)}}))),t.trigger("reset"),a.preventDefault()})),o.on("dragenter",(function(e){o.addClass("ai1wm-drag-over"),e.preventDefault()})),o.on("dragover",(function(e){o.addClass("ai1wm-drag-over"),e.preventDefault()})),o.on("dragleave",(function(e){o.removeClass("ai1wm-drag-over"),e.preventDefault()})),o.on("drop",(function(a){e.setDefaultValues(),o.removeClass("ai1wm-drag-over");var i=a.originalEvent.dataTransfer.files.item(0);i&&(e.fileSize=i.size,e.fileSize>ai1wm_uploader.max_file_size?e.model.setStatus({type:"pro",message:ai1wm_locale.import_from_file}):e.model.checkDiskSpace(e.fileSize,(function(){try{e.onFilesAdded(i),e.onBeforeUpload(i),e.upload(i)}catch(t){e.onError(t)}}))),t.trigger("reset"),a.preventDefault()}))},r.prototype.c1=function(e){if("wpress"!==e.name.substr(-6))throw new Error(ai1wm_locale.invalid_archive_extension)},r.prototype.c3=function(){if(ai1wm_compatibility.messages.length>0)throw new Error(ai1wm_compatibility.messages.join())},r.prototype.onFilesAdded=function(e){this.c1(e),this.c3(e),i(window).bind("beforeunload",(function(){return ai1wm_locale.stop_importing_your_website}))},r.prototype.onBeforeUpload=function(e){var t=this,a=Ai1wm.Util.random(12),o=Ai1wm.Util.form("#ai1wm-import-form").concat({name:"storage",value:a}).concat({name:"archive",value:e.name}).concat({name:"file",value:1});this.model.setParams(o),i.extend(ai1wm_uploader.params,{storage:a,archive:e.name}),this.model.onStop=function(){t.cancelUpload(),t.model.clean()},this.model.setStatus({type:"progress",percent:"0.00"})},r.prototype.upload=function(e){var t=this;this.xhr=null;var a=new FormData;for(var o in a.append("upload-file",e),ai1wm_uploader.params)a.append(o,ai1wm_uploader.params[o]);i.ajax({url:ai1wm_uploader.url,type:"POST",data:a,cache:!1,contentType:!1,processData:!1,xhr:function(){return t.xhr=i.ajaxSettings.xhr(),t.xhr.upload&&t.xhr.upload.addEventListener("progress",(function(e){var a=e.loaded/e.total*100;t.model.setStatus({type:"progress",percent:a.toFixed(2)})})),t.xhr},success:function(){t.stopUpload||t.onFileUploaded()},error:function(e,a){var o="";o=413===e.status?ai1wm_locale.file_too_large:0===e.status?ai1wm_locale.upload_failed_connection_lost:"".concat(ai1wm_locale.upload_failed," (").concat(e.status,"): ").concat(e.statusText||a),t.onError(new Error(o),!0)}})},r.prototype.cancelUpload=function(){this.xhr&&(this.xhr.abort(),this.stopUpload=!0)},r.prototype.onUploadProgress=function(e){this.model.setStatus({type:"progress",percent:e})},r.prototype.onFileUploaded=function(){this.model.start()},r.prototype.onError=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.model.setStatus({type:"error",title:ai1wm_locale.unable_to_import,message:e.message,leftAligned:t})},e.exports=r}},t={};function a(o){var i=t[o];if(void 0!==i)return i.exports;var r=t[o]={exports:{}};return e[o](r,r.exports,a),r.exports}a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}();var o=a(174),i=a(665);jQuery(document).ready((function(e){"use strict";(Ai1wm.MultisiteExtensionUploader?new Ai1wm.MultisiteExtensionUploader:Ai1wm.UnlimitedExtensionUploader?new Ai1wm.UnlimitedExtensionUploader:Ai1wm.FileExtensionUploader?new Ai1wm.FileExtensionUploader:new Ai1wm.FileUploader).init(),e(".ai1wm-expandable > div.ai1wm-button-main").on("click",(function(){e(this).parent().toggleClass("ai1wm-open")}))})),a.g.Ai1wm=jQuery.extend({},a.g.Ai1wm,{FileUploader:o,Import:i})})();